import { useState, useMemo } from 'react';
import './Sidebar.css';

const Sidebar = ({ activeItem, onItemClick, sidebarData }) => {
  const [collapsedItems, setCollapsedItems] = useState({});

  // 静态菜单项定义
  const staticMenuItems = [
    { id: 'CD-LPMT', label: 'CD-LPMT'},
    { id: 'WP-LPMT', label: 'WP-LPMT'},
    { id: 'SPMT-T/Q', label: 'SPMT-T/Q'},
    { id: 'TT', label: 'TT'},
    { id: 'CD-T/Q', label: 'CD-T/Q'},
    { id: 'WP-T/Q', label: 'WP-T/Q'},
    { id: 'MM(Multi-Message)', label: 'MM(Multi-Message)'},
    { id: 'LOW_E', label: 'LOW_E'},
    { id: 'TQ_TAO_CD', label: 'TQ_TAO_CD'},
    { id: 'TQ_TAO_TVT', label: 'TQ_TAO_TVT'},
    { id: 'TQ_TAO_WT', label: 'TQ_TAO_WT'},
    { id: 'TQ_TAO_CD_FEC', label: 'TQ_TAO_CD_FEC'},
  ];

  // 根据API数据动态生成菜单项
  const menuItems = useMemo(() => {
    if (!sidebarData) {
      // 如果没有API数据，返回静态菜单项（都不可选中）
      return staticMenuItems.map(item => ({
        ...item,
        disabled: true,
        hasChildren: false,
        children: []
      }));
    }

    return staticMenuItems.map(staticItem => {
      const apiData = sidebarData[staticItem.id];

      if (!apiData) {
        // 如果API数据中没有这个项目，设为禁用状态
        return {
          ...staticItem,
          disabled: true,
          hasChildren: false,
          children: []
        };
      }

      // 如果API数据中有这个项目，生成子菜单
      const children = Object.keys(apiData).map(eventKey => {
        const eventData = apiData[eventKey];
        const eventChildren = Object.keys(eventData).map(channelKey => ({
          id: `${staticItem.id}-${eventKey}-${channelKey}`,
          label: channelKey,
          disabled: false
        }));

        return {
          id: `${staticItem.id}-${eventKey}`,
          label: eventKey,
          hasChildren: eventChildren.length > 0,
          children: eventChildren,
          disabled: false
        };
      });

      return {
        ...staticItem,
        disabled: false,
        hasChildren: children.length > 0,
        children: children
      };
    });
  }, [sidebarData]);

  const handleToggleCollapse = (itemId) => {
    setCollapsedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  // 递归渲染菜单项的函数
  const renderMenuItem = (item, level = 0) => {
    const isCollapsed = collapsedItems[item.id];
    const hasActiveChild = activeItem && activeItem.startsWith(item.id + '-');
    const isDisabled = item.disabled;

    return (
      <li key={item.id} className={`sidebar__menu-item sidebar__menu-item--level-${level} ${isDisabled ? 'sidebar__menu-item--disabled' : ''}`}>
        {item.hasChildren ? (
          <>
            <button
              className={`sidebar__menu-link sidebar__menu-link--parent sidebar__menu-link--level-${level} ${
                activeItem === item.id || hasActiveChild ? 'sidebar__menu-link--active' : ''
              } ${isDisabled ? 'sidebar__menu-link--disabled' : ''}`}
              onClick={() => !isDisabled && handleToggleCollapse(item.id)}
              title={item.label}
              disabled={isDisabled}
            >
              <span className="sidebar__menu-text">{item.label}</span>
              <span className={`sidebar__menu-arrow ${isCollapsed ? 'sidebar__menu-arrow--collapsed' : ''}`}>
                ▼
              </span>
            </button>
            {!isDisabled && (
              <ul className={`sidebar__submenu sidebar__submenu--level-${level} ${isCollapsed ? 'sidebar__submenu--collapsed' : ''}`}>
                {item.children.map((child) => renderMenuItem(child, level + 1))}
              </ul>
            )}
          </>
        ) : (
          <button
            className={`sidebar__menu-link sidebar__menu-link--level-${level} ${
              activeItem === item.id ? 'sidebar__menu-link--active' : ''
            } ${isDisabled ? 'sidebar__menu-link--disabled' : ''}`}
            onClick={() => !isDisabled && onItemClick(item.id)}
            title={item.label}
            disabled={isDisabled}
          >
            <span className="sidebar__menu-text">{item.label}</span>
          </button>
        )}
      </li>
    );
  };

  return (
    <div className="sidebar sidebar--open">
      <div className="sidebar__header">
        <div className="sidebar__logo">
          <span className="sidebar__logo-text">单事例监测系统</span>
        </div>
      </div>

      <nav className="sidebar__nav">
        <ul className="sidebar__menu">
          {menuItems.map((item) => renderMenuItem(item))}
        </ul>
      </nav>

      {/* <div className="sidebar__footer">
        <div className="sidebar__user">
          <div className="sidebar__user-avatar">👤</div>
          <div className="sidebar__user-info">
            <div className="sidebar__user-name">管理员</div>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default Sidebar;
