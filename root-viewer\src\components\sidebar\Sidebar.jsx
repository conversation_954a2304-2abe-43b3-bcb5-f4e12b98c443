import { useState } from 'react';
import './Sidebar.css';

const Sidebar = ({ activeItem, onItemClick }) => {
  const [collapsedItems, setCollapsedItems] = useState({});

  const menuItems = [
    {
      id: 'CD-LPMT',
      label: 'CD-LPMT',
      hasChildren: true,
      children: [
        {
          id: 'CD-LPMT-0',
          label: 'CD-LPMT-0',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-0-0', label: '0' },
            { id: 'CD-LPMT-0-1', label: '1' },
            { id: 'CD-LPMT-0-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-1',
          label: 'CD-LPMT-1',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-1-0', label: '0' },
            { id: 'CD-LPMT-1-1', label: '1' },
            { id: 'CD-LPMT-1-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-2',
          label: 'CD-LPMT-2',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-2-0', label: '0' },
            { id: 'CD-LPMT-2-1', label: '1' },
            { id: 'CD-LPMT-2-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-3',
          label: 'CD-LPMT-3',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-3-0', label: '0' },
            { id: 'CD-LPMT-3-1', label: '1' },
            { id: 'CD-LPMT-3-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-4',
          label: 'CD-LPMT-4',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-4-0', label: '0' },
            { id: 'CD-LPMT-4-1', label: '1' },
            { id: 'CD-LPMT-4-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-5',
          label: 'CD-LPMT-5',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-5-0', label: '0' },
            { id: 'CD-LPMT-5-1', label: '1' },
            { id: 'CD-LPMT-5-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-6',
          label: 'CD-LPMT-6',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-6-0', label: '0' },
            { id: 'CD-LPMT-6-1', label: '1' },
            { id: 'CD-LPMT-6-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-7',
          label: 'CD-LPMT-7',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-7-0', label: '0' },
            { id: 'CD-LPMT-7-1', label: '1' },
            { id: 'CD-LPMT-7-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-8',
          label: 'CD-LPMT-8',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-8-0', label: '0' },
            { id: 'CD-LPMT-8-1', label: '1' },
            { id: 'CD-LPMT-8-2', label: '2' },
          ]
        },
        {
          id: 'CD-LPMT-9',
          label: 'CD-LPMT-9',
          hasChildren: true,
          children: [
            { id: 'CD-LPMT-9-0', label: '0' },
            { id: 'CD-LPMT-9-1', label: '1' },
            { id: 'CD-LPMT-9-2', label: '2' },
          ]
        },
      ]
    },
    { id: 'WP-LPMT', label: 'WP-LPMT'},
    { id: 'SPMT-T/Q', label: 'SPMT-T/Q'},
    { id: 'TT', label: 'TT'},
    { id: 'CD-T/Q', label: 'CD-T/Q'},
    { id: 'WP-T/Q', label: 'WP-T/Q'},
    { id: 'MM(Multi-Message)', label: 'MM(Multi-Message)'},
    { id: 'LOW_E', label: 'LOW_E'},
    { id: 'TQ_TAO_CD', label: 'TQ_TAO_CD'},
    { id: 'TQ_TAO_TVT', label: 'TQ_TAO_TVT'},
    { id: 'TQ_TAO_WT', label: 'TQ_TAO_WT'},
    { id: 'TQ_TAO_CD_FEC', label: 'TQ_TAO_CD_FEC'},
  ];

  const handleToggleCollapse = (itemId) => {
    setCollapsedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  // 递归渲染菜单项的函数
  const renderMenuItem = (item, level = 0) => {
    const isCollapsed = collapsedItems[item.id];
    const hasActiveChild = activeItem && activeItem.startsWith(item.id + '-');

    return (
      <li key={item.id} className={`sidebar__menu-item sidebar__menu-item--level-${level}`}>
        {item.hasChildren ? (
          <>
            <button
              className={`sidebar__menu-link sidebar__menu-link--parent sidebar__menu-link--level-${level} ${
                activeItem === item.id || hasActiveChild ? 'sidebar__menu-link--active' : ''
              }`}
              onClick={() => handleToggleCollapse(item.id)}
              title={item.label}
            >
              <span className="sidebar__menu-text">{item.label}</span>
              <span className={`sidebar__menu-arrow ${isCollapsed ? 'sidebar__menu-arrow--collapsed' : ''}`}>
                ▼
              </span>
            </button>
            <ul className={`sidebar__submenu sidebar__submenu--level-${level} ${isCollapsed ? 'sidebar__submenu--collapsed' : ''}`}>
              {item.children.map((child) => renderMenuItem(child, level + 1))}
            </ul>
          </>
        ) : (
          <button
            className={`sidebar__menu-link sidebar__menu-link--level-${level} ${
              activeItem === item.id ? 'sidebar__menu-link--active' : ''
            }`}
            onClick={() => onItemClick(item.id)}
            title={item.label}
          >
            <span className="sidebar__menu-text">{item.label}</span>
          </button>
        )}
      </li>
    );
  };

  return (
    <div className="sidebar sidebar--open">
      <div className="sidebar__header">
        <div className="sidebar__logo">
          <span className="sidebar__logo-text">单事例监测系统</span>
        </div>
      </div>

      <nav className="sidebar__nav">
        <ul className="sidebar__menu">
          {menuItems.map((item) => renderMenuItem(item))}
        </ul>
      </nav>

      {/* <div className="sidebar__footer">
        <div className="sidebar__user">
          <div className="sidebar__user-avatar">👤</div>
          <div className="sidebar__user-info">
            <div className="sidebar__user-name">管理员</div>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default Sidebar;
